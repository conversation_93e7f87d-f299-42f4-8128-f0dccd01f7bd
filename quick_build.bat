@echo off
echo ========================================
echo 快速构建测试
echo ========================================
echo.

echo 1. 清理构建缓存...
flutter clean

echo 2. 获取依赖包...
flutter pub get

echo 3. 尝试构建...
flutter build windows --release

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 构建成功！
    echo ========================================
    echo.
    echo 可执行文件位置: build\windows\runner\Release\installer_app.exe
    echo.
    
    set /p run_app="是否要运行应用？(y/n): "
    if /i "%run_app%"=="y" (
        start "" "build\windows\runner\Release\installer_app.exe"
    )
) else (
    echo.
    echo ========================================
    echo 构建失败！
    echo ========================================
    echo.
    echo 请检查上面的错误信息。
)

pause
