import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:installer_app/services/file_service.dart';
import 'package:installer_app/services/system_service.dart';
import 'package:installer_app/models/installer_config.dart';

void main() {
  group('Installer Tests', () {
    late FileService fileService;
    late SystemService systemService;
    late InstallerConfig config;

    setUp(() {
      fileService = FileService();
      systemService = SystemService();
      config = InstallerConfig(
        appName: 'TestApp',
        version: '1.0.0',
        publisher: 'Test Publisher',
        installPath: r'C:\Temp\TestApp',
      );
    });

    test('File Service - Create Directory', () async {
      const testPath = r'C:\Temp\InstallerTest';
      
      await fileService.createDirectory(testPath);
      
      expect(await Directory(testPath).exists(), isTrue);
      
      // 清理
      await Directory(testPath).delete(recursive: true);
    });

    test('File Service - Check File Exists', () async {
      const testFile = r'C:\Temp\test_file.txt';
      
      // 创建测试文件
      await File(testFile).writeAsString('test content');
      
      expect(await fileService.fileExists(testFile), isTrue);
      expect(await fileService.fileExists(r'C:\Temp\non_existent.txt'), isFalse);
      
      // 清理
      await File(testFile).delete();
    });

    test('File Service - Get File Size', () async {
      const testFile = r'C:\Temp\size_test.txt';
      const testContent = 'Hello, World!';
      
      await File(testFile).writeAsString(testContent);
      
      final size = await fileService.getFileSize(testFile);
      expect(size, equals(testContent.length));
      
      // 清理
      await File(testFile).delete();
    });

    test('File Service - Format File Size', () {
      expect(FileService.formatFileSize(512), equals('512 B'));
      expect(FileService.formatFileSize(1024), equals('1.0 KB'));
      expect(FileService.formatFileSize(1024 * 1024), equals('1.0 MB'));
      expect(FileService.formatFileSize(1024 * 1024 * 1024), equals('1.0 GB'));
    });

    test('System Service - Check Admin Rights', () {
      final isAdmin = systemService.isRunningAsAdmin();
      expect(isAdmin, isA<bool>());
    });

    test('Installer Config - Copy With', () {
      final newConfig = config.copyWith(
        appName: 'NewTestApp',
        version: '2.0.0',
      );
      
      expect(newConfig.appName, equals('NewTestApp'));
      expect(newConfig.version, equals('2.0.0'));
      expect(newConfig.publisher, equals(config.publisher)); // 未更改的值应保持不变
      expect(newConfig.installPath, equals(config.installPath));
    });

    test('Installer Config - Executable Path', () {
      expect(config.executablePath, equals(r'C:\Temp\TestApp\TestApp.exe'));
    });

    test('Installer Config - Uninstall Key', () {
      expect(config.uninstallKey, 
        equals(r'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\TestApp'));
    });

    group('File Operations', () {
      const testDir = r'C:\Temp\FileOperationsTest';
      
      setUp(() async {
        await Directory(testDir).create(recursive: true);
      });

      tearDown(() async {
        if (await Directory(testDir).exists()) {
          await Directory(testDir).delete(recursive: true);
        }
      });

      test('Copy Application Files', () async {
        // 这个测试需要 app_files 目录存在
        if (await Directory('app_files').exists()) {
          await fileService.copyApplicationFiles(testDir);
          
          // 检查文件是否被复制
          expect(await Directory(testDir).exists(), isTrue);
          
          final files = await Directory(testDir).list().toList();
          expect(files.isNotEmpty, isTrue);
        }
      });

      test('Get Directory Size', () async {
        // 创建一些测试文件
        await File('$testDir\\file1.txt').writeAsString('Hello');
        await File('$testDir\\file2.txt').writeAsString('World');
        
        final size = await fileService.getDirectorySize(testDir);
        expect(size, greaterThan(0));
      });
    });

    group('Disk Space Tests', () {
      test('Check Disk Space', () async {
        final hasSpace = await fileService.hasEnoughSpace(r'C:\', 1024); // 1KB
        expect(hasSpace, isTrue);
      });
    });
  });

  group('Integration Tests', () {
    test('Full Installation Simulation', () async {
      final fileService = FileService();
      final systemService = SystemService();
      final config = InstallerConfig(
        appName: 'IntegrationTestApp',
        version: '1.0.0',
        publisher: 'Test Publisher',
        installPath: r'C:\Temp\IntegrationTest',
      );

      try {
        // 步骤1: 创建安装目录
        await fileService.createDirectory(config.installPath);
        expect(await Directory(config.installPath).exists(), isTrue);

        // 步骤2: 复制文件（如果存在）
        if (await Directory('app_files').exists()) {
          await fileService.copyApplicationFiles(config.installPath);
        }

        // 步骤3: 验证安装
        expect(await Directory(config.installPath).exists(), isTrue);

        print('Integration test completed successfully!');
      } finally {
        // 清理
        if (await Directory(config.installPath).exists()) {
          await Directory(config.installPath).delete(recursive: true);
        }
      }
    });
  });
}

/// 运行性能测试
void runPerformanceTests() {
  group('Performance Tests', () {
    test('File Copy Performance', () async {
      final stopwatch = Stopwatch()..start();
      
      const testDir = r'C:\Temp\PerformanceTest';
      final fileService = FileService();
      
      await fileService.createDirectory(testDir);
      
      // 创建一些测试文件
      for (int i = 0; i < 100; i++) {
        await File('$testDir\\file_$i.txt').writeAsString('Test content $i');
      }
      
      stopwatch.stop();
      print('Created 100 files in ${stopwatch.elapsedMilliseconds}ms');
      
      // 清理
      await Directory(testDir).delete(recursive: true);
      
      expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 应该在5秒内完成
    });
  });
}

/// 手动测试辅助函数
void printSystemInfo() {
  print('=== 系统信息 ===');
  print('操作系统: ${Platform.operatingSystem}');
  print('操作系统版本: ${Platform.operatingSystemVersion}');
  print('可执行文件路径: ${Platform.resolvedExecutable}');
  print('环境变量:');
  Platform.environment.forEach((key, value) {
    if (key.contains('PATH') || key.contains('TEMP') || key.contains('USER')) {
      print('  $key: $value');
    }
  });
}

/// 运行所有测试
void runAllTests() {
  print('开始运行安装器测试...\n');
  
  printSystemInfo();
  print('\n');
  
  main();
  runPerformanceTests();
  
  print('\n测试完成！');
}
