import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import '../providers/installer_provider.dart';

class PathSelectionStep extends StatefulWidget {
  const PathSelectionStep({super.key});

  @override
  State<PathSelectionStep> createState() => _PathSelectionStepState();
}

class _PathSelectionStepState extends State<PathSelectionStep> {
  late TextEditingController _pathController;
  String? _pathError;

  @override
  void initState() {
    super.initState();
    final provider = Provider.of<InstallerProvider>(context, listen: false);
    _pathController = TextEditingController(text: provider.config.installPath);
  }

  @override
  void dispose() {
    _pathController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InstallerProvider>(
      builder: (context, provider, child) {
        return Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                '选择安装位置',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '请选择 ${provider.config.appName} 的安装目录',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 32),

              // 安装路径选择
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '安装目录',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _pathController,
                              decoration: InputDecoration(
                                hintText: '选择安装目录...',
                                border: const OutlineInputBorder(),
                                errorText: _pathError,
                                prefixIcon: const Icon(Icons.folder),
                              ),
                              onChanged: (value) {
                                setState(() {
                                  _pathError = _validatePath(value);
                                });
                                if (_pathError == null) {
                                  provider.updateInstallPath(value);
                                }
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          ElevatedButton.icon(
                            onPressed: _selectPath,
                            icon: const Icon(Icons.folder_open),
                            label: const Text('浏览'),
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildSpaceInfo(),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // 快捷方式选项
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '快捷方式选项',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 16),
                      CheckboxListTile(
                        title: const Text('创建桌面快捷方式'),
                        subtitle: const Text('在桌面创建应用程序快捷方式'),
                        value: provider.config.createDesktopShortcut,
                        onChanged: (value) {
                          provider.updateShortcutSettings(desktop: value);
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      CheckboxListTile(
                        title: const Text('创建开始菜单快捷方式'),
                        subtitle: const Text('在开始菜单中创建程序组'),
                        value: provider.config.createStartMenuShortcut,
                        onChanged: (value) {
                          provider.updateShortcutSettings(startMenu: value);
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      CheckboxListTile(
                        title: const Text('添加到系统PATH'),
                        subtitle: const Text('将应用程序添加到系统环境变量'),
                        value: provider.config.addToPath,
                        onChanged: (value) {
                          provider.updateShortcutSettings(addToPath: value);
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                    ],
                  ),
                ),
              ),

              const Spacer(),

              // 安装信息摘要
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '安装摘要',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildSummaryRow('应用程序', provider.config.appName),
                    _buildSummaryRow('版本', provider.config.version),
                    _buildSummaryRow('发布商', provider.config.publisher),
                    _buildSummaryRow('安装位置', provider.config.installPath),
                    _buildSummaryRow('所需空间', '约 50 MB'), // 这里可以动态计算
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: Colors.grey.shade700),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpaceInfo() {
    return FutureBuilder<Map<String, int>>(
      future: _getDiskSpace(_pathController.text),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          final data = snapshot.data!;
          final freeSpace = data['free'] ?? 0;
          final totalSpace = data['total'] ?? 0;
          final usedSpace = totalSpace - freeSpace;
          final freeSpaceMB = (freeSpace / (1024 * 1024)).round();
          final totalSpaceMB = (totalSpace / (1024 * 1024)).round();

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '磁盘空间',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: totalSpace > 0 ? usedSpace / totalSpace : 0,
                backgroundColor: Colors.grey.shade300,
                valueColor: AlwaysStoppedAnimation<Color>(
                  freeSpaceMB > 100 ? Colors.green : Colors.orange,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '可用空间: ${freeSpaceMB} MB / ${totalSpaceMB} MB',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Future<void> _selectPath() async {
    final result = await FilePicker.platform.getDirectoryPath(
      dialogTitle: '选择安装目录',
      initialDirectory: _pathController.text,
    );

    if (result != null) {
      setState(() {
        _pathController.text = result;
        _pathError = _validatePath(result);
      });
      
      if (_pathError == null) {
        final provider = Provider.of<InstallerProvider>(context, listen: false);
        provider.updateInstallPath(result);
      }
    }
  }

  String? _validatePath(String path) {
    if (path.isEmpty) {
      return '请选择安装目录';
    }

    // 检查路径是否有效
    try {
      final directory = Directory(path);
      final parent = directory.parent;
      if (!parent.existsSync()) {
        return '父目录不存在';
      }
    } catch (e) {
      return '无效的路径';
    }

    // 检查是否有写入权限（简单检查）
    if (path.startsWith('C:\\Windows') || path.startsWith('C:\\Program Files')) {
      // 这些目录通常需要管理员权限
    }

    return null;
  }

  Future<Map<String, int>> _getDiskSpace(String path) async {
    try {
      if (path.isEmpty) return {'free': 0, 'total': 0};
      
      final directory = Directory(path);
      final parent = directory.existsSync() ? directory : directory.parent;
      
      if (Platform.isWindows) {
        // 在Windows上获取磁盘空间信息
        final result = await Process.run(
          'wmic',
          ['logicaldisk', 'where', 'caption="${path.substring(0, 2)}"', 'get', 'size,freespace', '/format:csv'],
          runInShell: true,
        );
        
        if (result.exitCode == 0) {
          final lines = result.stdout.toString().split('\n');
          for (final line in lines) {
            if (line.contains(',') && !line.startsWith('Node')) {
              final parts = line.split(',');
              if (parts.length >= 3) {
                final freeSpace = int.tryParse(parts[1]) ?? 0;
                final totalSpace = int.tryParse(parts[2]) ?? 0;
                return {'free': freeSpace, 'total': totalSpace};
              }
            }
          }
        }
      }
      
      return {'free': 0, 'total': 0};
    } catch (e) {
      return {'free': 0, 'total': 0};
    }
  }
}
