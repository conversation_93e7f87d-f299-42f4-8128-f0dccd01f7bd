import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../providers/installer_provider.dart';

class ErrorStep extends StatefulWidget {
  const ErrorStep({super.key});

  @override
  State<ErrorStep> createState() => _ErrorStepState();
}

class _ErrorStepState extends State<ErrorStep> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _shakeAnimation;
  bool _showDetails = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _shakeAnimation = Tween<double>(
      begin: -10.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticIn,
    ));

    // 启动摇摆动画
    _animationController.repeat(reverse: true);
    
    // 2秒后停止动画
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _animationController.stop();
        _animationController.reset();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InstallerProvider>(
      builder: (context, provider, child) {
        return Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 错误图标
              AnimatedBuilder(
                animation: _shakeAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(_shakeAnimation.value, 0),
                    child: Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.red.withOpacity(0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.error,
                        size: 80,
                        color: Colors.red.shade600,
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 32),

              // 错误标题
              Text(
                '安装失败',
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                '很抱歉，${provider.config.appName} 安装过程中出现了错误',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // 错误信息卡片
              Card(
                elevation: 4,
                color: Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.warning,
                            color: Colors.red.shade600,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '错误详情',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.red.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '错误信息:',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: Colors.red.shade700,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              provider.errorMessage,
                              style: TextStyle(
                                color: Colors.grey.shade700,
                                fontFamily: 'monospace',
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // 显示详细信息按钮
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _showDetails = !_showDetails;
                          });
                        },
                        icon: Icon(
                          _showDetails ? Icons.expand_less : Icons.expand_more,
                          color: Colors.red.shade600,
                        ),
                        label: Text(
                          _showDetails ? '隐藏详细信息' : '显示详细信息',
                          style: TextStyle(color: Colors.red.shade600),
                        ),
                      ),
                      
                      // 详细信息
                      if (_showDetails) ...[
                        const SizedBox(height: 16),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildDetailRow('应用程序', provider.config.appName),
                              _buildDetailRow('版本', provider.config.version),
                              _buildDetailRow('安装路径', provider.config.installPath),
                              _buildDetailRow('操作系统', Platform.operatingSystem),
                              _buildDetailRow('时间', DateTime.now().toString()),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // 解决方案建议
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.lightbulb_outline,
                            color: Colors.orange.shade600,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '解决方案建议',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildSuggestion(
                        '检查磁盘空间',
                        '确保安装目录所在磁盘有足够的可用空间',
                        Icons.storage,
                      ),
                      _buildSuggestion(
                        '检查权限',
                        '右键点击安装程序，选择"以管理员身份运行"',
                        Icons.admin_panel_settings,
                      ),
                      _buildSuggestion(
                        '更换安装目录',
                        '选择用户目录下的位置，如 C:\\Users\\<USER>\\AppData\\Local\\应用名',
                        Icons.folder_open,
                      ),
                      _buildSuggestion(
                        '关闭杀毒软件',
                        '暂时关闭杀毒软件或将安装程序添加到白名单',
                        Icons.security,
                      ),
                      _buildSuggestion(
                        '更换安装路径',
                        '选择其他目录进行安装',
                        Icons.folder_open,
                      ),
                    ],
                  ),
                ),
              ),

              const Spacer(),

              // 操作按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    onPressed: () => _copyErrorToClipboard(provider),
                    icon: const Icon(Icons.copy),
                    label: const Text('复制错误信息'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade100,
                      foregroundColor: Colors.grey.shade700,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton.icon(
                    onPressed: () => _retryInstallation(provider),
                    icon: const Icon(Icons.refresh),
                    label: const Text('重试安装'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton.icon(
                    onPressed: () => _exitInstaller(),
                    icon: const Icon(Icons.close),
                    label: const Text('退出'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: Colors.grey.shade700,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestion(String title, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.orange.shade600,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _copyErrorToClipboard(InstallerProvider provider) {
    final errorInfo = '''
安装错误报告
================
应用程序: ${provider.config.appName}
版本: ${provider.config.version}
安装路径: ${provider.config.installPath}
操作系统: ${Platform.operatingSystem}
时间: ${DateTime.now()}

错误信息:
${provider.errorMessage}
''';

    Clipboard.setData(ClipboardData(text: errorInfo));
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('错误信息已复制到剪贴板'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _retryInstallation(InstallerProvider provider) {
    // 重置到欢迎页面，让用户重新开始安装
    // 这里可以添加清理逻辑
    // provider.reset();
  }

  void _exitInstaller() {
    // 退出安装程序
    // SystemNavigator.pop();
  }
}
