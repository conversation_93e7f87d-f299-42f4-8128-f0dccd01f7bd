import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/installer_provider.dart';
import '../widgets/welcome_step.dart';
import '../widgets/license_step.dart';
import '../widgets/path_selection_step.dart';
import '../widgets/installation_step.dart';
import '../widgets/completion_step.dart';
import '../widgets/error_step.dart';

class InstallerScreen extends StatelessWidget {
  const InstallerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<InstallerProvider>(
        builder: (context, provider, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue.shade50,
                  Colors.white,
                  Colors.blue.shade50,
                ],
              ),
            ),
            child: Column(
              children: [
                // 标题栏
                _buildTitleBar(context, provider),
                
                // 主要内容区域
                Expanded(
                  child: _buildCurrentStep(context, provider),
                ),
                
                // 底部按钮栏
                if (provider.currentStep != InstallerStep.installing)
                  _buildBottomBar(context, provider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTitleBar(BuildContext context, InstallerProvider provider) {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.blue.shade600,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.install_desktop,
            color: Colors.white,
            size: 32,
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${provider.config.appName} 安装向导',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '版本 ${provider.config.version}',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const Spacer(),
          // 步骤指示器
          _buildStepIndicator(provider),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(InstallerProvider provider) {
    final steps = [
      InstallerStep.welcome,
      InstallerStep.license,
      InstallerStep.selectPath,
      InstallerStep.installing,
      InstallerStep.completed,
    ];

    return Row(
      children: steps.asMap().entries.map((entry) {
        final index = entry.key;
        final step = entry.value;
        final isActive = provider.currentStep == step;
        final isCompleted = steps.indexOf(provider.currentStep) > index;
        
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          child: CircleAvatar(
            radius: 12,
            backgroundColor: isActive 
                ? Colors.white 
                : isCompleted 
                    ? Colors.green.shade400 
                    : Colors.white.withOpacity(0.3),
            child: Text(
              '${index + 1}',
              style: TextStyle(
                color: isActive 
                    ? Colors.blue.shade600 
                    : isCompleted 
                        ? Colors.white 
                        : Colors.white.withOpacity(0.7),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCurrentStep(BuildContext context, InstallerProvider provider) {
    switch (provider.currentStep) {
      case InstallerStep.welcome:
        return const WelcomeStep();
      case InstallerStep.license:
        return const LicenseStep();
      case InstallerStep.selectPath:
        return const PathSelectionStep();
      case InstallerStep.installing:
        return const InstallationStep();
      case InstallerStep.completed:
        return const CompletionStep();
      case InstallerStep.error:
        return const ErrorStep();
    }
  }

  Widget _buildBottomBar(BuildContext context, InstallerProvider provider) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 取消按钮
          TextButton(
            onPressed: () => _showCancelDialog(context),
            child: const Text('取消'),
          ),
          
          // 导航按钮
          Row(
            children: [
              // 上一步按钮
              if (provider.currentStep != InstallerStep.welcome &&
                  provider.currentStep != InstallerStep.completed &&
                  provider.currentStep != InstallerStep.error)
                ElevatedButton(
                  onPressed: provider.previousStep,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade200,
                    foregroundColor: Colors.grey.shade700,
                  ),
                  child: const Text('上一步'),
                ),
              
              const SizedBox(width: 12),
              
              // 下一步/完成按钮
              ElevatedButton(
                onPressed: _getNextButtonAction(provider),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                child: Text(_getNextButtonText(provider)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  VoidCallback? _getNextButtonAction(InstallerProvider provider) {
    switch (provider.currentStep) {
      case InstallerStep.completed:
        return () => _exitApplication();
      case InstallerStep.error:
        return null;
      default:
        return provider.nextStep;
    }
  }

  String _getNextButtonText(InstallerProvider provider) {
    switch (provider.currentStep) {
      case InstallerStep.welcome:
        return '下一步';
      case InstallerStep.license:
        return '我同意';
      case InstallerStep.selectPath:
        return '安装';
      case InstallerStep.completed:
        return '完成';
      case InstallerStep.error:
        return '关闭';
      default:
        return '下一步';
    }
  }

  void _showCancelDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认取消'),
        content: const Text('您确定要取消安装吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('继续安装'),
          ),
          ElevatedButton(
            onPressed: () => _exitApplication(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('取消安装'),
          ),
        ],
      ),
    );
  }

  void _exitApplication() {
    // 在实际应用中，这里应该清理临时文件等
    // 然后退出应用
    // SystemNavigator.pop(); // 或者使用 exit(0)
  }
}
