import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/installer_config.dart';
import '../services/file_service.dart';
import '../services/simple_system_service.dart';

enum InstallerStep {
  welcome,
  license,
  selectPath,
  installing,
  completed,
  error
}

class InstallerProvider extends ChangeNotifier {
  InstallerStep _currentStep = InstallerStep.welcome;
  double _progress = 0.0;
  String _statusMessage = '';
  String _errorMessage = '';
  late InstallerConfig _config;
  
  final FileService _fileService = FileService();
  final SimpleSystemService _systemService = SimpleSystemService();

  InstallerStep get currentStep => _currentStep;
  double get progress => _progress;
  String get statusMessage => _statusMessage;
  String get errorMessage => _errorMessage;
  InstallerConfig get config => _config;

  InstallerProvider() {
    _initializeConfig();
  }

  void _initializeConfig() {
    // 使用用户目录作为默认安装路径，避免权限问题
    final userProfile = Platform.environment['USERPROFILE'] ?? r'C:\Users\<USER>\\AppData\\Local\\我的应用';

    _config = InstallerConfig(
      appName: '我的应用',
      version: '1.0.0',
      publisher: '我的公司',
      installPath: defaultPath,
      createDesktopShortcut: true,
      createStartMenuShortcut: true,
      addToPath: false,
    );
  }

  void nextStep() {
    switch (_currentStep) {
      case InstallerStep.welcome:
        _currentStep = InstallerStep.license;
        break;
      case InstallerStep.license:
        _currentStep = InstallerStep.selectPath;
        break;
      case InstallerStep.selectPath:
        _currentStep = InstallerStep.installing;
        _startInstallation();
        break;
      case InstallerStep.installing:
        _currentStep = InstallerStep.completed;
        break;
      case InstallerStep.completed:
      case InstallerStep.error:
        // 安装完成或出错，可以关闭应用
        break;
    }
    notifyListeners();
  }

  void previousStep() {
    switch (_currentStep) {
      case InstallerStep.license:
        _currentStep = InstallerStep.welcome;
        break;
      case InstallerStep.selectPath:
        _currentStep = InstallerStep.license;
        break;
      case InstallerStep.installing:
      case InstallerStep.completed:
      case InstallerStep.error:
        // 这些步骤不允许返回
        return;
      case InstallerStep.welcome:
        // 已经是第一步
        return;
    }
    notifyListeners();
  }

  void updateInstallPath(String path) {
    _config = _config.copyWith(installPath: path);
    notifyListeners();
  }

  void updateShortcutSettings({
    bool? desktop,
    bool? startMenu,
    bool? addToPath,
  }) {
    _config = _config.copyWith(
      createDesktopShortcut: desktop ?? _config.createDesktopShortcut,
      createStartMenuShortcut: startMenu ?? _config.createStartMenuShortcut,
      addToPath: addToPath ?? _config.addToPath,
    );
    notifyListeners();
  }

  Future<void> _startInstallation() async {
    try {
      _progress = 0.0;
      _statusMessage = '准备安装...';
      notifyListeners();

      // 步骤0: 检查权限
      _statusMessage = '检查安装权限...';
      _progress = 0.05;
      notifyListeners();

      if (!await _checkAndRequestPermissions()) {
        throw Exception('无法获取安装权限。请以管理员身份运行安装器，或选择其他安装目录。');
      }

      // 步骤1: 创建安装目录
      _statusMessage = '创建安装目录...';
      _progress = 0.1;
      notifyListeners();
      await _fileService.createDirectory(_config.installPath);

      // 步骤2: 复制文件
      _statusMessage = '复制应用文件...';
      _progress = 0.3;
      notifyListeners();
      await _fileService.copyApplicationFiles(
        _config.installPath,
        onProgress: (progress, currentFile) {
          _statusMessage = '复制文件: $currentFile';
          _progress = 0.3 + (progress * 0.4); // 0.3 到 0.7 的范围
          notifyListeners();
        },
      );

      // 步骤3: 创建快捷方式
      if (_config.createDesktopShortcut) {
        _statusMessage = '创建桌面快捷方式...';
        _progress = 0.7;
        notifyListeners();
        await _systemService.createDesktopShortcut(_config);
      }

      if (_config.createStartMenuShortcut) {
        _statusMessage = '创建开始菜单快捷方式...';
        _progress = 0.8;
        notifyListeners();
        await _systemService.createStartMenuShortcut(_config);
      }

      // 步骤4: 注册应用
      _statusMessage = '注册应用信息...';
      _progress = 0.9;
      notifyListeners();
      await _systemService.registerApplication(_config);

      // 完成
      _statusMessage = '安装完成！';
      _progress = 1.0;
      notifyListeners();

      // 等待一下再跳转到完成页面
      await Future.delayed(const Duration(seconds: 1));
      _currentStep = InstallerStep.completed;
      notifyListeners();

    } catch (e) {
      _errorMessage = '安装失败: ${e.toString()}';
      _currentStep = InstallerStep.error;
      notifyListeners();
    }
  }

  /// 检查并请求安装权限
  Future<bool> _checkAndRequestPermissions() async {
    try {
      // 首先尝试在目标目录创建测试文件
      final testDir = Directory(_config.installPath);
      final testFile = File('${_config.installPath}\\installer_test.tmp');

      try {
        // 尝试创建目录和测试文件
        await testDir.create(recursive: true);
        await testFile.writeAsString('test');
        await testFile.delete();
        return true; // 有权限
      } catch (e) {
        // 没有权限，检查是否需要管理员权限
        if (_config.installPath.toLowerCase().contains('program files') ||
            _config.installPath.toLowerCase().contains('windows') ||
            _config.installPath.startsWith(r'C:\') && !_config.installPath.toLowerCase().contains('users')) {

          // 需要管理员权限的目录
          if (_systemService.isRunningAsAdmin()) {
            // 已经是管理员，但仍然失败
            return false;
          } else {
            // 尝试请求管理员权限
            _statusMessage = '请求管理员权限...';
            notifyListeners();

            // 显示权限提示对话框
            return await _showPermissionDialog();
          }
        } else {
          // 其他权限问题
          return false;
        }
      }
    } catch (e) {
      return false;
    }
  }

  /// 显示权限请求对话框
  Future<bool> _showPermissionDialog() async {
    // 这里应该显示一个对话框让用户选择
    // 由于我们在Provider中，需要通过其他方式处理UI
    // 暂时返回false，让用户手动以管理员身份运行
    return false;
  }
}
