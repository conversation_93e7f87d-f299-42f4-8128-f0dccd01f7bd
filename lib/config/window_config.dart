import 'package:flutter/material.dart';

/// 窗口配置类
class WindowConfig {
  // 窗口尺寸
  static const double windowWidth = 900;
  static const double windowHeight = 700;
  static const Size windowSize = Size(windowWidth, windowHeight);
  
  // 最小窗口尺寸
  static const double minWindowWidth = 800;
  static const double minWindowHeight = 600;
  static const Size minWindowSize = Size(minWindowWidth, minWindowHeight);
  
  // 最大窗口尺寸（与窗口尺寸相同，实现固定大小）
  static const Size maxWindowSize = windowSize;
  
  // 窗口标题
  static const String windowTitle = '应用安装向导';
  
  // 窗口是否可调整大小
  static const bool resizable = false;
  
  // 窗口是否可最大化
  static const bool maximizable = false;
  
  // 窗口是否居中显示
  static const bool centerWindow = true;
  
  // 窗口背景色
  static const Color backgroundColor = Colors.transparent;
  
  // 是否在任务栏显示
  static const bool skipTaskbar = false;
  
  // 标题栏样式
  static const String titleBarStyle = 'normal';
  
  // 窗口按钮可见性
  static const bool windowButtonVisibility = false;
  
  /// 获取窗口配置摘要
  static Map<String, dynamic> getConfigSummary() {
    return {
      'width': windowWidth,
      'height': windowHeight,
      'resizable': resizable,
      'maximizable': maximizable,
      'title': windowTitle,
      'centered': centerWindow,
    };
  }
  
  /// 验证窗口尺寸是否有效
  static bool isValidSize(Size size) {
    return size.width >= minWindowWidth && 
           size.height >= minWindowHeight &&
           size.width <= 1920 && 
           size.height <= 1080;
  }
  
  /// 获取适合屏幕的窗口尺寸
  static Size getAdaptiveSize(Size screenSize) {
    // 如果屏幕太小，调整窗口大小
    if (screenSize.width < windowWidth || screenSize.height < windowHeight) {
      final adaptiveWidth = (screenSize.width * 0.9).clamp(minWindowWidth, windowWidth);
      final adaptiveHeight = (screenSize.height * 0.9).clamp(minWindowHeight, windowHeight);
      return Size(adaptiveWidth, adaptiveHeight);
    }
    return windowSize;
  }
  
  /// 计算窗口居中位置
  static Offset getCenterPosition(Size screenSize, Size windowSize) {
    final x = (screenSize.width - windowSize.width) / 2;
    final y = (screenSize.height - windowSize.height) / 2;
    return Offset(x.clamp(0, screenSize.width - windowSize.width), 
                  y.clamp(0, screenSize.height - windowSize.height));
  }
}
