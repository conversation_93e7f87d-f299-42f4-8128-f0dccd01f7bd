import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/installer_screen.dart';
import 'providers/installer_provider.dart';

void main() {
  runApp(const InstallerApp());
}

class InstallerApp extends StatelessWidget {
  const InstallerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => InstallerProvider(),
      child: MaterialApp(
        title: '应用安装器',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF2196F3),
            brightness: Brightness.light,
          ),
          useMaterial3: true,
        ),
        home: const InstallerScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
