import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';
import 'package:win32/win32.dart';
import 'package:path/path.dart' as path;
import '../models/installer_config.dart';

class SystemService {
  /// 创建桌面快捷方式
  Future<void> createDesktopShortcut(InstallerConfig config) async {
    if (!Platform.isWindows) return;
    
    try {
      final desktopPath = _getDesktopPath();
      final shortcutPath = path.join(desktopPath, '${config.appName}.lnk');
      
      await _createShortcut(
        shortcutPath: shortcutPath,
        targetPath: config.executablePath,
        workingDirectory: config.installPath,
        description: config.appName,
        iconPath: config.appIcon ?? config.executablePath,
      );
    } catch (e) {
      print('创建桌面快捷方式失败: $e');
    }
  }

  /// 创建开始菜单快捷方式
  Future<void> createStartMenuShortcut(InstallerConfig config) async {
    if (!Platform.isWindows) return;
    
    try {
      final startMenuPath = _getStartMenuPath();
      final programsPath = path.join(startMenuPath, 'Programs', config.publisher);
      
      // 创建发布商文件夹
      await Directory(programsPath).create(recursive: true);
      
      final shortcutPath = path.join(programsPath, '${config.appName}.lnk');
      
      await _createShortcut(
        shortcutPath: shortcutPath,
        targetPath: config.executablePath,
        workingDirectory: config.installPath,
        description: config.appName,
        iconPath: config.appIcon ?? config.executablePath,
      );
      
      // 创建卸载快捷方式
      final uninstallShortcutPath = path.join(programsPath, '卸载 ${config.appName}.lnk');
      await _createShortcut(
        shortcutPath: uninstallShortcutPath,
        targetPath: 'msiexec.exe',
        arguments: '/x ${config.uninstallKey}',
        description: '卸载 ${config.appName}',
      );
    } catch (e) {
      print('创建开始菜单快捷方式失败: $e');
    }
  }

  /// 注册应用程序到Windows注册表
  Future<void> registerApplication(InstallerConfig config) async {
    if (!Platform.isWindows) return;
    
    try {
      final uninstallKey = config.uninstallKey;
      
      // 打开注册表项
      final hKey = calloc<HKEY>();
      final result = RegCreateKeyEx(
        HKEY_LOCAL_MACHINE,
        uninstallKey.toNativeUtf16(),
        0,
        nullptr,
        REG_OPTION_NON_VOLATILE,
        KEY_WRITE,
        nullptr,
        hKey,
        nullptr,
      );
      
      if (result == ERROR_SUCCESS) {
        // 设置应用信息
        _setRegistryValue(hKey.value, 'DisplayName', config.appName);
        _setRegistryValue(hKey.value, 'DisplayVersion', config.version);
        _setRegistryValue(hKey.value, 'Publisher', config.publisher);
        _setRegistryValue(hKey.value, 'InstallLocation', config.installPath);
        _setRegistryValue(hKey.value, 'UninstallString', 'msiexec.exe /x ${config.uninstallKey}');
        _setRegistryValue(hKey.value, 'DisplayIcon', config.executablePath);
        
        // 设置安装日期
        final now = DateTime.now();
        final installDate = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
        _setRegistryValue(hKey.value, 'InstallDate', installDate);
        
        // 设置估计大小（KB）
        final installSize = await _getInstallSize(config.installPath);
        _setRegistryDwordValue(hKey.value, 'EstimatedSize', installSize ~/ 1024);
        
        RegCloseKey(hKey.value);
      }
      
      calloc.free(hKey);
    } catch (e) {
      print('注册应用程序失败: $e');
    }
  }

  /// 创建快捷方式
  Future<void> _createShortcut({
    required String shortcutPath,
    required String targetPath,
    String? workingDirectory,
    String? arguments,
    String? description,
    String? iconPath,
  }) async {
    // 使用PowerShell创建快捷方式
    final script = '''
\$WshShell = New-Object -comObject WScript.Shell
\$Shortcut = \$WshShell.CreateShortcut("$shortcutPath")
\$Shortcut.TargetPath = "$targetPath"
${workingDirectory != null ? '\$Shortcut.WorkingDirectory = "$workingDirectory"' : ''}
${arguments != null ? '\$Shortcut.Arguments = "$arguments"' : ''}
${description != null ? '\$Shortcut.Description = "$description"' : ''}
${iconPath != null ? '\$Shortcut.IconLocation = "$iconPath"' : ''}
\$Shortcut.Save()
''';

    final result = await Process.run(
      'powershell.exe',
      ['-Command', script],
      runInShell: true,
    );
    
    if (result.exitCode != 0) {
      throw Exception('PowerShell执行失败: ${result.stderr}');
    }
  }

  /// 获取桌面路径
  String _getDesktopPath() {
    final buffer = calloc<Uint16>(MAX_PATH);
    final result = SHGetFolderPath(0, CSIDL_DESKTOP, 0, SHGFP_TYPE_CURRENT, buffer);
    
    if (result == S_OK) {
      final path = buffer.toDartString();
      calloc.free(buffer);
      return path;
    }
    
    calloc.free(buffer);
    return path.join(Platform.environment['USERPROFILE'] ?? '', 'Desktop');
  }

  /// 获取开始菜单路径
  String _getStartMenuPath() {
    final buffer = calloc<Uint16>(MAX_PATH);
    final result = SHGetFolderPath(0, CSIDL_STARTMENU, 0, SHGFP_TYPE_CURRENT, buffer);
    
    if (result == S_OK) {
      final path = buffer.toDartString();
      calloc.free(buffer);
      return path;
    }
    
    calloc.free(buffer);
    return path.join(Platform.environment['APPDATA'] ?? '', 'Microsoft', 'Windows', 'Start Menu');
  }

  /// 设置注册表字符串值
  void _setRegistryValue(int hKey, String valueName, String value) {
    final valueNamePtr = valueName.toNativeUtf16();
    final valuePtr = value.toNativeUtf16();
    
    RegSetValueEx(
      hKey,
      valueNamePtr,
      0,
      REG_SZ,
      valuePtr.cast<Uint8>(),
      (value.length + 1) * 2,
    );
    
    calloc.free(valueNamePtr);
    calloc.free(valuePtr);
  }

  /// 设置注册表DWORD值
  void _setRegistryDwordValue(int hKey, String valueName, int value) {
    final valueNamePtr = valueName.toNativeUtf16();
    final valuePtr = calloc<Uint32>();
    valuePtr.value = value;
    
    RegSetValueEx(
      hKey,
      valueNamePtr,
      0,
      REG_DWORD,
      valuePtr.cast<Uint8>(),
      4,
    );
    
    calloc.free(valueNamePtr);
    calloc.free(valuePtr);
  }

  /// 获取安装大小
  Future<int> _getInstallSize(String installPath) async {
    int totalSize = 0;
    final directory = Directory(installPath);
    
    if (await directory.exists()) {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          try {
            totalSize += await entity.length();
          } catch (e) {
            // 忽略无法访问的文件
          }
        }
      }
    }
    
    return totalSize;
  }

  /// 检查是否以管理员权限运行
  bool isRunningAsAdmin() {
    try {
      final result = Process.runSync(
        'net',
        ['session'],
        runInShell: true,
      );
      return result.exitCode == 0;
    } catch (e) {
      return false;
    }
  }

  /// 请求管理员权限
  Future<bool> requestAdminPrivileges() async {
    if (isRunningAsAdmin()) return true;
    
    try {
      final currentExe = Platform.resolvedExecutable;
      final result = await Process.run(
        'powershell.exe',
        [
          '-Command',
          'Start-Process -FilePath "$currentExe" -Verb RunAs -Wait'
        ],
        runInShell: true,
      );
      
      return result.exitCode == 0;
    } catch (e) {
      return false;
    }
  }
}
