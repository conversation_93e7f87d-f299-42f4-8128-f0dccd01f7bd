import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';
import 'package:win32/win32.dart';
import 'package:path/path.dart' as path;
import '../models/uninstaller_config.dart';

class UninstallerService {
  /// 卸载应用程序
  Future<void> uninstallApplication(
    UninstallerConfig config,
    Function(double progress, String message) onProgress,
  ) async {
    try {
      onProgress(0.0, '准备卸载...');
      await Future.delayed(const Duration(milliseconds: 500));

      // 步骤1: 停止应用程序进程
      onProgress(0.1, '停止应用程序进程...');
      await _terminateApplicationProcesses(config.appName);

      // 步骤2: 删除快捷方式
      if (config.removeShortcuts) {
        onProgress(0.3, '删除快捷方式...');
        await _removeShortcuts(config);
      }

      // 步骤3: 删除应用程序文件
      onProgress(0.5, '删除应用程序文件...');
      await _removeApplicationFiles(config.installPath);

      // 步骤4: 删除用户数据（如果选择）
      if (config.removeUserData) {
        onProgress(0.7, '删除用户数据...');
        await _removeUserData(config);
      }

      // 步骤5: 清理注册表
      if (config.removeRegistry) {
        onProgress(0.9, '清理注册表...');
        await _removeRegistryEntries(config);
      }

      onProgress(1.0, '卸载完成！');
    } catch (e) {
      throw Exception('卸载失败: ${e.toString()}');
    }
  }

  /// 停止应用程序进程
  Future<void> _terminateApplicationProcesses(String appName) async {
    try {
      if (Platform.isWindows) {
        // 使用taskkill命令停止进程
        final exeName = '$appName.exe';
        final result = await Process.run(
          'taskkill',
          ['/F', '/IM', exeName],
          runInShell: true,
        );
        
        // 忽略进程不存在的错误
        if (result.exitCode != 0 && !result.stderr.toString().contains('not found')) {
          print('Warning: Could not terminate process $exeName: ${result.stderr}');
        }
      }
    } catch (e) {
      print('Warning: Error terminating processes: $e');
    }
  }

  /// 删除快捷方式
  Future<void> _removeShortcuts(UninstallerConfig config) async {
    try {
      // 删除桌面快捷方式
      final desktopPath = _getDesktopPath();
      final desktopShortcut = File(path.join(desktopPath, '${config.appName}.lnk'));
      if (await desktopShortcut.exists()) {
        await desktopShortcut.delete();
      }

      // 删除开始菜单快捷方式
      final startMenuPath = _getStartMenuPath();
      final programsPath = path.join(startMenuPath, 'Programs', config.publisher);
      final programsDir = Directory(programsPath);
      
      if (await programsDir.exists()) {
        // 删除应用程序快捷方式
        final appShortcut = File(path.join(programsPath, '${config.appName}.lnk'));
        if (await appShortcut.exists()) {
          await appShortcut.delete();
        }

        // 删除卸载快捷方式
        final uninstallShortcut = File(path.join(programsPath, '卸载 ${config.appName}.lnk'));
        if (await uninstallShortcut.exists()) {
          await uninstallShortcut.delete();
        }

        // 如果目录为空，删除发布商目录
        final contents = await programsDir.list().toList();
        if (contents.isEmpty) {
          await programsDir.delete();
        }
      }
    } catch (e) {
      print('Warning: Error removing shortcuts: $e');
    }
  }

  /// 删除应用程序文件
  Future<void> _removeApplicationFiles(String installPath) async {
    try {
      final installDir = Directory(installPath);
      if (await installDir.exists()) {
        await installDir.delete(recursive: true);
      }
    } catch (e) {
      throw Exception('无法删除应用程序文件: $e');
    }
  }

  /// 删除用户数据
  Future<void> _removeUserData(UninstallerConfig config) async {
    try {
      // 删除AppData中的用户数据
      final appDataPath = Platform.environment['APPDATA'];
      if (appDataPath != null) {
        final userDataDir = Directory(path.join(appDataPath, config.appName));
        if (await userDataDir.exists()) {
          await userDataDir.delete(recursive: true);
        }
      }

      // 删除LocalAppData中的数据
      final localAppDataPath = Platform.environment['LOCALAPPDATA'];
      if (localAppDataPath != null) {
        final localDataDir = Directory(path.join(localAppDataPath, config.appName));
        if (await localDataDir.exists()) {
          await localDataDir.delete(recursive: true);
        }
      }
    } catch (e) {
      print('Warning: Error removing user data: $e');
    }
  }

  /// 清理注册表项
  Future<void> _removeRegistryEntries(UninstallerConfig config) async {
    try {
      if (!Platform.isWindows) return;

      // 删除卸载信息
      final uninstallKey = config.uninstallKey;
      await _deleteRegistryKey(HKEY_LOCAL_MACHINE, uninstallKey);

      // 删除其他可能的注册表项
      final appKey = 'SOFTWARE\\${config.publisher}\\${config.appName}';
      await _deleteRegistryKey(HKEY_LOCAL_MACHINE, appKey);
      await _deleteRegistryKey(HKEY_CURRENT_USER, appKey);
    } catch (e) {
      print('Warning: Error removing registry entries: $e');
    }
  }

  /// 删除注册表项
  Future<void> _deleteRegistryKey(int hKey, String keyPath) async {
    try {
      final keyPathPtr = keyPath.toNativeUtf16();
      final result = RegDeleteKey(hKey, keyPathPtr);
      calloc.free(keyPathPtr);
      
      if (result != ERROR_SUCCESS && result != ERROR_FILE_NOT_FOUND) {
        print('Warning: Could not delete registry key $keyPath: $result');
      }
    } catch (e) {
      print('Warning: Error deleting registry key $keyPath: $e');
    }
  }

  /// 获取桌面路径
  String _getDesktopPath() {
    final buffer = calloc<Uint16>(MAX_PATH);
    final result = SHGetFolderPath(0, CSIDL_DESKTOP, 0, SHGFP_TYPE_CURRENT, buffer);
    
    if (result == S_OK) {
      final path = buffer.toDartString();
      calloc.free(buffer);
      return path;
    }
    
    calloc.free(buffer);
    return path.join(Platform.environment['USERPROFILE'] ?? '', 'Desktop');
  }

  /// 获取开始菜单路径
  String _getStartMenuPath() {
    final buffer = calloc<Uint16>(MAX_PATH);
    final result = SHGetFolderPath(0, CSIDL_STARTMENU, 0, SHGFP_TYPE_CURRENT, buffer);
    
    if (result == S_OK) {
      final path = buffer.toDartString();
      calloc.free(buffer);
      return path;
    }
    
    calloc.free(buffer);
    return path.join(Platform.environment['APPDATA'] ?? '', 'Microsoft', 'Windows', 'Start Menu');
  }

  /// 检查应用程序是否正在运行
  Future<bool> isApplicationRunning(String appName) async {
    try {
      if (Platform.isWindows) {
        final result = await Process.run(
          'tasklist',
          ['/FI', 'IMAGENAME eq $appName.exe'],
          runInShell: true,
        );
        
        return result.stdout.toString().contains('$appName.exe');
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// 获取应用程序占用的磁盘空间
  Future<int> getApplicationSize(String installPath) async {
    try {
      final directory = Directory(installPath);
      int totalSize = 0;
      
      if (await directory.exists()) {
        await for (final entity in directory.list(recursive: true)) {
          if (entity is File) {
            try {
              totalSize += await entity.length();
            } catch (e) {
              // 忽略无法访问的文件
            }
          }
        }
      }
      
      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  /// 验证卸载权限
  Future<bool> validateUninstallPermissions(String installPath) async {
    try {
      final directory = Directory(installPath);
      if (!await directory.exists()) {
        return false;
      }

      // 尝试在安装目录创建临时文件来测试写权限
      final tempFile = File(path.join(installPath, 'temp_uninstall_test.tmp'));
      await tempFile.writeAsString('test');
      await tempFile.delete();
      
      return true;
    } catch (e) {
      return false;
    }
  }
}
