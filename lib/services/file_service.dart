import 'dart:io';
import 'dart:typed_data';
import 'package:archive/archive.dart';
import 'package:path/path.dart' as path;
import 'package:http/http.dart' as http;

class FileService {
  /// 创建目录
  Future<void> createDirectory(String dirPath) async {
    final directory = Directory(dirPath);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
  }

  /// 复制应用文件到安装目录
  Future<void> copyApplicationFiles(String installPath) async {
    // 这里你需要根据实际情况修改
    // 可以从内置资源、网络下载或从指定路径复制文件
    
    // 示例：从assets复制文件（需要在pubspec.yaml中配置assets）
    // await _copyFromAssets(installPath);
    
    // 示例：从网络下载文件
    // await _downloadAndExtract(installPath);
    
    // 示例：从本地路径复制
    await _copyFromLocalPath(installPath);
  }

  /// 从本地路径复制文件（开发时使用）
  Future<void> _copyFromLocalPath(String installPath) async {
    // 这里假设你的应用文件在项目的 'app_files' 目录中
    final sourceDir = Directory('app_files');
    if (await sourceDir.exists()) {
      await _copyDirectory(sourceDir, Directory(installPath));
    } else {
      // 如果没有app_files目录，创建一个示例文件
      await _createSampleFiles(installPath);
    }
  }

  /// 创建示例文件（用于测试）
  Future<void> _createSampleFiles(String installPath) async {
    final exeFile = File(path.join(installPath, 'MyApp.exe'));
    await exeFile.writeAsString('示例可执行文件内容');
    
    final configFile = File(path.join(installPath, 'config.ini'));
    await configFile.writeAsString('[Settings]\nVersion=1.0.0\n');
    
    final readmeFile = File(path.join(installPath, 'README.txt'));
    await readmeFile.writeAsString('这是一个示例应用程序。\n安装完成！');
  }

  /// 递归复制目录
  Future<void> _copyDirectory(Directory source, Directory destination) async {
    await destination.create(recursive: true);
    
    await for (final entity in source.list(recursive: false)) {
      if (entity is Directory) {
        final newDirectory = Directory(path.join(destination.path, path.basename(entity.path)));
        await _copyDirectory(entity, newDirectory);
      } else if (entity is File) {
        final newFile = File(path.join(destination.path, path.basename(entity.path)));
        await entity.copy(newFile.path);
      }
    }
  }

  /// 从网络下载并解压文件
  Future<void> _downloadAndExtract(String installPath, {String? downloadUrl}) async {
    if (downloadUrl == null) return;
    
    final response = await http.get(Uri.parse(downloadUrl));
    if (response.statusCode == 200) {
      final archive = ZipDecoder().decodeBytes(response.bodyBytes);
      
      for (final file in archive) {
        final filename = file.name;
        final filePath = path.join(installPath, filename);
        
        if (file.isFile) {
          final data = file.content as List<int>;
          await File(filePath).create(recursive: true);
          await File(filePath).writeAsBytes(data);
        } else {
          await Directory(filePath).create(recursive: true);
        }
      }
    }
  }

  /// 删除目录及其内容
  Future<void> deleteDirectory(String dirPath) async {
    final directory = Directory(dirPath);
    if (await directory.exists()) {
      await directory.delete(recursive: true);
    }
  }

  /// 检查文件是否存在
  Future<bool> fileExists(String filePath) async {
    return await File(filePath).exists();
  }

  /// 获取文件大小
  Future<int> getFileSize(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      return await file.length();
    }
    return 0;
  }

  /// 获取目录大小
  Future<int> getDirectorySize(String dirPath) async {
    final directory = Directory(dirPath);
    int totalSize = 0;
    
    if (await directory.exists()) {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
    }
    
    return totalSize;
  }
}
