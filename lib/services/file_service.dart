import 'dart:io';
import 'dart:typed_data';
import 'package:archive/archive.dart';
import 'package:path/path.dart' as path;
import 'package:http/http.dart' as http;

class FileService {
  /// 创建目录
  Future<void> createDirectory(String dirPath) async {
    final directory = Directory(dirPath);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
  }

  /// 复制应用文件到安装目录
  Future<void> copyApplicationFiles(String installPath, {
    Function(double progress, String currentFile)? onProgress,
  }) async {
    // 这里你需要根据实际情况修改
    // 可以从内置资源、网络下载或从指定路径复制文件

    onProgress?.call(0.0, '准备复制文件...');

    // 示例：从assets复制文件（需要在pubspec.yaml中配置assets）
    // await _copyFromAssets(installPath, onProgress: onProgress);

    // 示例：从网络下载文件
    // await _downloadAndExtract(installPath, onProgress: onProgress);

    // 示例：从本地路径复制
    await _copyFromLocalPath(installPath, onProgress: onProgress);

    onProgress?.call(1.0, '文件复制完成');
  }

  /// 从本地路径复制文件（开发时使用）
  Future<void> _copyFromLocalPath(String installPath, {
    Function(double progress, String currentFile)? onProgress,
  }) async {
    // 这里假设你的应用文件在项目的 'app_files' 目录中
    final sourceDir = Directory('app_files');
    if (await sourceDir.exists()) {
      await _copyDirectory(sourceDir, Directory(installPath), onProgress: onProgress);
    } else {
      // 如果没有app_files目录，创建一个示例文件
      await _createSampleFiles(installPath, onProgress: onProgress);
    }
  }

  /// 创建示例文件（用于测试）
  Future<void> _createSampleFiles(String installPath, {
    Function(double progress, String currentFile)? onProgress,
  }) async {
    onProgress?.call(0.2, 'MyApp.exe');
    final exeFile = File(path.join(installPath, 'MyApp.exe'));
    await exeFile.writeAsString('示例可执行文件内容');

    onProgress?.call(0.6, 'config.ini');
    final configFile = File(path.join(installPath, 'config.ini'));
    await configFile.writeAsString('[Settings]\nVersion=1.0.0\n');

    onProgress?.call(1.0, 'README.txt');
    final readmeFile = File(path.join(installPath, 'README.txt'));
    await readmeFile.writeAsString('这是一个示例应用程序。\n安装完成！');
  }

  /// 递归复制目录
  Future<void> _copyDirectory(Directory source, Directory destination, {
    Function(double progress, String currentFile)? onProgress,
  }) async {
    await destination.create(recursive: true);

    // 计算总文件数用于进度报告
    final allFiles = <FileSystemEntity>[];
    await for (final entity in source.list(recursive: true)) {
      if (entity is File) {
        allFiles.add(entity);
      }
    }

    int copiedFiles = 0;
    await for (final entity in source.list(recursive: false)) {
      if (entity is Directory) {
        final newDirectory = Directory(path.join(destination.path, path.basename(entity.path)));
        await _copyDirectory(entity, newDirectory, onProgress: onProgress);
      } else if (entity is File) {
        final fileName = path.basename(entity.path);
        onProgress?.call(copiedFiles / allFiles.length, fileName);

        final newFile = File(path.join(destination.path, fileName));
        await entity.copy(newFile.path);
        copiedFiles++;
      }
    }
  }

  /// 从网络下载并解压文件
  Future<void> downloadAndExtract(String installPath, String downloadUrl, {
    Function(double progress, String currentFile)? onProgress,
  }) async {
    onProgress?.call(0.0, '开始下载...');

    final response = await http.get(Uri.parse(downloadUrl));
    if (response.statusCode == 200) {
      onProgress?.call(0.3, '下载完成，开始解压...');

      final archive = ZipDecoder().decodeBytes(response.bodyBytes);
      final totalFiles = archive.length;
      int extractedFiles = 0;

      for (final file in archive) {
        final filename = file.name;
        final filePath = path.join(installPath, filename);

        onProgress?.call(0.3 + (extractedFiles / totalFiles) * 0.7, filename);

        if (file.isFile) {
          final data = file.content as List<int>;
          await File(filePath).create(recursive: true);
          await File(filePath).writeAsBytes(data);
        } else {
          await Directory(filePath).create(recursive: true);
        }

        extractedFiles++;
      }

      onProgress?.call(1.0, '解压完成');
    } else {
      throw Exception('下载失败: HTTP ${response.statusCode}');
    }
  }

  /// 删除目录及其内容
  Future<void> deleteDirectory(String dirPath) async {
    final directory = Directory(dirPath);
    if (await directory.exists()) {
      await directory.delete(recursive: true);
    }
  }

  /// 检查文件是否存在
  Future<bool> fileExists(String filePath) async {
    return await File(filePath).exists();
  }

  /// 获取文件大小
  Future<int> getFileSize(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      return await file.length();
    }
    return 0;
  }

  /// 获取目录大小
  Future<int> getDirectorySize(String dirPath) async {
    final directory = Directory(dirPath);
    int totalSize = 0;

    if (await directory.exists()) {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
    }

    return totalSize;
  }

  /// 验证文件完整性（简单的大小检查）
  Future<bool> validateFileIntegrity(String filePath, int expectedSize) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) return false;

      final actualSize = await file.length();
      return actualSize == expectedSize;
    } catch (e) {
      return false;
    }
  }

  /// 创建备份
  Future<void> createBackup(String sourcePath, String backupPath) async {
    final source = Directory(sourcePath);
    final backup = Directory(backupPath);

    if (await source.exists()) {
      await _copyDirectory(source, backup);
    }
  }

  /// 检查磁盘空间是否足够
  Future<bool> hasEnoughSpace(String path, int requiredBytes) async {
    try {
      if (Platform.isWindows) {
        final drive = path.substring(0, 2); // 例如 "C:"
        final result = await Process.run(
          'wmic',
          ['logicaldisk', 'where', 'caption="$drive"', 'get', 'freespace', '/format:csv'],
          runInShell: true,
        );

        if (result.exitCode == 0) {
          final lines = result.stdout.toString().split('\n');
          for (final line in lines) {
            if (line.contains(',') && !line.startsWith('Node')) {
              final parts = line.split(',');
              if (parts.length >= 2) {
                final freeSpace = int.tryParse(parts[1]) ?? 0;
                return freeSpace >= requiredBytes;
              }
            }
          }
        }
      }
      return true; // 如果无法检查，假设有足够空间
    } catch (e) {
      return true;
    }
  }

  /// 格式化文件大小
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
