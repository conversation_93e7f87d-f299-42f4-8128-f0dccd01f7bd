import 'dart:io';
import 'package:flutter/material.dart';

class PermissionService {
  /// 检查是否以管理员身份运行
  static bool isRunningAsAdmin() {
    try {
      final result = Process.runSync(
        'net',
        ['session'],
        runInShell: true,
      );
      return result.exitCode == 0;
    } catch (e) {
      return false;
    }
  }

  /// 请求管理员权限（重启应用）
  static Future<bool> requestAdminPrivileges() async {
    if (isRunningAsAdmin()) return true;
    
    try {
      final currentExe = Platform.resolvedExecutable;
      
      // 使用PowerShell以管理员身份重启应用
      final script = '''
Start-Process -FilePath "$currentExe" -Verb RunAs
''';

      final result = await Process.run(
        'powershell.exe',
        ['-Command', script],
        runInShell: true,
      );
      
      if (result.exitCode == 0) {
        // 成功启动管理员版本，退出当前进程
        exit(0);
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }

  /// 显示权限请求对话框
  static Future<bool> showPermissionDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const PermissionDialog();
      },
    ) ?? false;
  }

  /// 检查路径是否需要管理员权限
  static bool pathRequiresAdmin(String path) {
    final lowerPath = path.toLowerCase();
    return lowerPath.contains('program files') ||
           lowerPath.contains('windows') ||
           lowerPath.startsWith('c:\\') && !lowerPath.contains('users');
  }

  /// 测试路径写权限
  static Future<bool> testWritePermission(String path) async {
    try {
      final testDir = Directory(path);
      final testFile = File('$path\\permission_test.tmp');
      
      // 尝试创建目录和文件
      await testDir.create(recursive: true);
      await testFile.writeAsString('test');
      await testFile.delete();
      
      return true;
    } catch (e) {
      return false;
    }
  }
}

/// 权限请求对话框
class PermissionDialog extends StatelessWidget {
  const PermissionDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: const Color(0xFFFFF8DC), // 类似Windows UAC的背景色
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      title: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.blue.shade600,
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Icon(
              Icons.security,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              '用户账户控制',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1F1F1F),
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(false),
            icon: const Icon(Icons.close, size: 20),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(
              minWidth: 24,
              minHeight: 24,
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '你要允许来自未知发布者的此应用对你的设备进行更改吗？',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF1F1F1F),
              height: 1.4,
            ),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  Platform.resolvedExecutable.split('\\').last,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F1F1F),
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '发布者: 未知',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF666666),
                  ),
                ),
                Text(
                  '文件源: 此计算机上的硬盘驱动器',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          InkWell(
            onTap: () => _showMoreDetails(context),
            child: Text(
              '显示更多详细信息',
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue.shade700,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SizedBox(
              width: 80,
              height: 32,
              child: OutlinedButton(
                onPressed: () => Navigator.of(context).pop(false),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.grey.shade400),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                child: const Text(
                  '否',
                  style: TextStyle(
                    fontSize: 13,
                    color: Color(0xFF1F1F1F),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: 80,
              height: 32,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  elevation: 1,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                child: const Text(
                  '是',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
      actionsPadding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
      contentPadding: const EdgeInsets.fromLTRB(24, 8, 24, 0),
      titlePadding: const EdgeInsets.fromLTRB(24, 16, 16, 8),
    );
  }

  void _showMoreDetails(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('程序详细信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('程序名称', Platform.resolvedExecutable.split('\\').last),
            _buildDetailRow('发布者', '未知'),
            _buildDetailRow('文件版本', '1.0.0'),
            _buildDetailRow('文件路径', Platform.resolvedExecutable),
            _buildDetailRow('数字签名', '无'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ],
      ),
    );
  }
}
