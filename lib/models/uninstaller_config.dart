class UninstallerConfig {
  final String appName;
  final String version;
  final String publisher;
  final String installPath;
  final bool removeUserData;
  final bool removeRegistry;
  final bool removeShortcuts;
  final String? appIcon;
  final DateTime? installDate;
  final int? estimatedSize;

  UninstallerConfig({
    required this.appName,
    required this.version,
    required this.publisher,
    required this.installPath,
    this.removeUserData = false,
    this.removeRegistry = true,
    this.removeShortcuts = true,
    this.appIcon,
    this.installDate,
    this.estimatedSize,
  });

  String get executablePath => '$installPath\\${appName}.exe';
  String get uninstallKey => 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\$appName';

  UninstallerConfig copyWith({
    String? appName,
    String? version,
    String? publisher,
    String? installPath,
    bool? removeUserData,
    bool? removeRegistry,
    bool? removeShortcuts,
    String? appIcon,
    DateTime? installDate,
    int? estimatedSize,
  }) {
    return UninstallerConfig(
      appName: appName ?? this.appName,
      version: version ?? this.version,
      publisher: publisher ?? this.publisher,
      installPath: installPath ?? this.installPath,
      removeUserData: removeUserData ?? this.removeUserData,
      removeRegistry: removeRegistry ?? this.removeRegistry,
      removeShortcuts: removeShortcuts ?? this.removeShortcuts,
      appIcon: appIcon ?? this.appIcon,
      installDate: installDate ?? this.installDate,
      estimatedSize: estimatedSize ?? this.estimatedSize,
    );
  }

  /// 从注册表信息创建卸载配置
  factory UninstallerConfig.fromRegistry(Map<String, dynamic> registryData) {
    return UninstallerConfig(
      appName: registryData['DisplayName'] ?? 'Unknown App',
      version: registryData['DisplayVersion'] ?? '1.0.0',
      publisher: registryData['Publisher'] ?? 'Unknown Publisher',
      installPath: registryData['InstallLocation'] ?? '',
      appIcon: registryData['DisplayIcon'],
      installDate: _parseInstallDate(registryData['InstallDate']),
      estimatedSize: registryData['EstimatedSize'],
    );
  }

  static DateTime? _parseInstallDate(dynamic dateString) {
    if (dateString == null) return null;
    
    try {
      final dateStr = dateString.toString();
      if (dateStr.length == 8) {
        // Format: YYYYMMDD
        final year = int.parse(dateStr.substring(0, 4));
        final month = int.parse(dateStr.substring(4, 6));
        final day = int.parse(dateStr.substring(6, 8));
        return DateTime(year, month, day);
      }
    } catch (e) {
      // 忽略解析错误
    }
    
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'appName': appName,
      'version': version,
      'publisher': publisher,
      'installPath': installPath,
      'removeUserData': removeUserData,
      'removeRegistry': removeRegistry,
      'removeShortcuts': removeShortcuts,
      'appIcon': appIcon,
      'installDate': installDate?.toIso8601String(),
      'estimatedSize': estimatedSize,
    };
  }

  factory UninstallerConfig.fromJson(Map<String, dynamic> json) {
    return UninstallerConfig(
      appName: json['appName'] ?? '',
      version: json['version'] ?? '',
      publisher: json['publisher'] ?? '',
      installPath: json['installPath'] ?? '',
      removeUserData: json['removeUserData'] ?? false,
      removeRegistry: json['removeRegistry'] ?? true,
      removeShortcuts: json['removeShortcuts'] ?? true,
      appIcon: json['appIcon'],
      installDate: json['installDate'] != null 
          ? DateTime.parse(json['installDate']) 
          : null,
      estimatedSize: json['estimatedSize'],
    );
  }
}
